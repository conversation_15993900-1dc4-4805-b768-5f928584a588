// pages/login/login.js
const app = getApp();

Page({
  data: {
    privacyChecked: false,
    showAuthModal: false,
    showUserInfoModal: false,
    showPrivacyDialog: false,
    isLoggingIn: false,
    tempUserInfo: {
      nickName: '微信用户',
      avatarUrl: '/images/default-avatar.png',
      gender: 0
    }
  },
  
  onLoad: function () {
    // 获取app实例
    const app = getApp();
    
    // 检查全局数据中是否有 token
    if (app.globalData.token && !app.globalData.useMockUser) {
      console.log("用户已登录，跳转到主页");
      this.navigateToMain();
      return;
    }
    
    // 检查本地存储中是否有 token
    const token = wx.getStorageSync('token');
    if (token && !token.startsWith('mock_')) {
      console.log("本地存在真实token，设置到全局并跳转到主页");
      app.globalData.token = token;
      
      // 检查并加载用户信息
      const userInfoStr = wx.getStorageSync('userInfo');
      if (userInfoStr) {
        try {
          app.globalData.userInfo = JSON.parse(userInfoStr);
        } catch (e) {
          console.error("解析存储的用户信息失败:", e);
        }
      }
      
      // 后台验证 token
      app.checkTokenValidity(token);
      
      // 先跳转到主页，避免用户等待
      this.navigateToMain();
    } else if (token && token.startsWith('mock_')) {
      console.log("检测到体验账号token，等待用户登录");
      // 不进行任何跳转，等待用户进行正式登录
    }
  },
  
  // Toggle privacy checkbox
  togglePrivacyCheckbox: function() {
    this.setData({
      privacyChecked: !this.data.privacyChecked
    });
  },
  
  // Navigate to privacy policy page
  navigateToPrivacyPolicy: function() {
    this.setData({
      showPrivacyDialog: true
    });
  },
  
  // Close privacy dialog
  closePrivacyDialog: function() {
    this.setData({
      showPrivacyDialog: false
    });
  },
  
  // Agree to privacy policy
  agreePrivacyPolicy: function() {
    this.setData({
      privacyChecked: true,
      showPrivacyDialog: false
    });
  },
  
  // Disagree privacy policy
  disagreePrivacyPolicy: function() {
    this.setData({
      privacyChecked: false,
      showPrivacyDialog: false
    });
  },
  
  // Handle login button click
  handleLogin: function() {
    // Check if privacy policy is accepted
    if (!this.data.privacyChecked) {
      this.setData({
        showPrivacyDialog: true
      });
      return;
    }
    
    // Show auth modal
    this.setData({
      showAuthModal: true
    });
  },
  
  // Cancel auth
  cancelAuth: function() {
    this.setData({
      showAuthModal: false
    });
  },
  
  // Cancel user info modal
  cancelUserInfo: function() {
    this.setData({
      showUserInfoModal: false
    });
  },
  
  // Prevent modal close when clicking modal content
  preventClose: function() {
    // Do nothing, just prevent event propagation
  },
  
  // 处理选择头像事件
  onChooseAvatar: function(e) {
    const { avatarUrl } = e.detail;
    this.setData({
      'tempUserInfo.avatarUrl': avatarUrl
    });
  },
  
  // 处理输入昵称事件
  onInputNickname: function(e) {
    this.setData({
      'tempUserInfo.nickName': e.detail.value
    });
  },
  
  // 昵称审核事件
  onNicknameReview: function(e) {
    // 可以在这里处理昵称审核结果
    console.log('昵称审核结果:', e.detail);
  },
  
  // 跳过用户信息填写
  skipUserInfo: function() {
    this.setData({
      showUserInfoModal: false
    });
    this.navigateToMain();
  },
  
  // 保存用户信息
  saveUserInfo: function() {
    const { nickName, avatarUrl } = this.data.tempUserInfo;
    
    // 检查昵称是否为空
    if (!nickName || nickName.trim() === '') {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }
    
    // 更新用户信息
    app.updateUserInfo({
      nickName: nickName,
      avatarUrl: avatarUrl,
      gender: this.data.tempUserInfo.gender
    }, (updateRes) => {
      this.setData({
        showUserInfoModal: false
      });
      
      // 无论结果如何，都跳转到主页
      this.navigateToMain();
    });
  },
  
  // Confirm auth and proceed with login
  confirmAuth: function() {
    // Prevent multiple clicks
    if (this.data.isLoggingIn) {
      return;
    }
    
    this.setData({
      isLoggingIn: true
    });
    
    // Hide auth modal
    this.setData({
      showAuthModal: false
    });
    
    // Show loading
    wx.showLoading({
      title: '登录中...',
      mask: true
    });
    
    // Call login function from app
    app.login((res) => {
      wx.hideLoading();
      
      this.setData({
        isLoggingIn: false
      });
      
      if (res.success) {
        // 无论是否新用户，都显示获取用户信息的弹窗
        this.setData({
          showUserInfoModal: true
        });
      } else {
        // Show error
        wx.showToast({
          title: res.error || '登录失败',
          icon: 'none'
        });
      }
    });
  },
  
  // Get user profile (不再使用，保留代码以备参考)
  getUserProfile: function() {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        // Update user profile
        app.updateUserInfo({
          nickName: res.userInfo.nickName,
          avatarUrl: res.userInfo.avatarUrl,
          gender: res.userInfo.gender
        }, (updateRes) => {
          // Navigate to main page regardless of update result
          this.navigateToMain();
        });
      },
      fail: () => {
        // User denied, still go to main page
        this.navigateToMain();
      }
    });
  },
  
  // Navigate to main page
  navigateToMain: function() {
    // 设置全局刷新标志，确保登录后自动刷新数据
    getApp().globalData.needRefreshClothes = true;
    
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  
  // 清除本地存储数据
  clearStorageData: function() {
    const app = getApp();
    app.clearStorage();
    
    // 显示提示
    wx.showToast({
      title: '数据已重置',
      icon: 'success',
      duration: 1500
    });
  }
}) 