// 共同管理衣橱主页
// 模块1：圈子基础管理模块

const app = getApp();

Page({
  data: {
    loading: true,
    hasCircle: false,
    userRole: null, // 'creator' 或 'member'
    circle: null,
    showCreateModal: false,
    showJoinModal: false,
    showKickModal: false,
    showLeaveModal: false,
    showDissolveModal: false,

    // 创建圈子表单
    createForm: {
      name: '',
      description: ''
    },

    // 加入圈子表单
    joinForm: {
      invitationCode: ''
    },

    // 当前操作的成员
    currentMember: null,

    // 加载状态
    submitting: false,

    // 防重复显示通知
    hasShownRemovedNotification: false
  },

  onLoad: function(options) {
    // 重置页面级别的通知状态
    this.setData({ hasShownRemovedNotification: false });
    this.loadCircleInfo();
  },

  onShow: function() {
    // 每次显示页面时刷新数据，但不重置通知状态
    this.loadCircleInfo();
  },

  // 加载圈子信息
  loadCircleInfo: function() {
    this.setData({ loading: true });
    
    wx.request({
      url: app.globalData.apiBaseUrl + '/get_circle_info.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('获取圈子信息响应:', res.data);
        
        if (res.data.status === 'success') {
          console.log('设置圈子数据:', {
            hasCircle: res.data.data.has_circle,
            userRole: res.data.data.user_role,
            circle: res.data.data.circle
          });

          this.setData({
            hasCircle: res.data.data.has_circle,
            userRole: res.data.data.user_role,
            circle: res.data.data.circle,
            loading: false
          });

          console.log('页面数据设置后:', {
            hasCircle: this.data.hasCircle,
            userRole: this.data.userRole,
            loading: this.data.loading
          });

          // 检查用户是否被踢出圈子（防重复显示）
          if (!res.data.data.has_circle &&
              res.data.data.removed_info &&
              res.data.data.removed_info.was_removed) {

            // 检查这个具体的通知是否已经显示过
            const removedInfo = res.data.data.removed_info;
            const notificationKey = `notification_read_${app.globalData.userInfo?.id || 'unknown'}_${removedInfo.removed_at}`;
            const hasReadThisNotification = wx.getStorageSync(notificationKey) || false;

            if (!hasReadThisNotification && !this.data.hasShownRemovedNotification) {
              console.log('显示被踢出通知:', removedInfo);
              this.setData({ hasShownRemovedNotification: true });
              this.showRemovedNotification(removedInfo);
            } else {
              console.log('通知已读，不重复显示:', notificationKey, hasReadThisNotification);
            }
          } else {
            console.log('不显示通知 - has_circle:', res.data.data.has_circle,
                       'removed_info:', res.data.data.removed_info);
          }
        } else {
          wx.showToast({
            title: res.data.message || '获取圈子信息失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('获取圈子信息失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 显示创建圈子弹框
  showCreateCircle: function() {
    this.setData({ 
      showCreateModal: true,
      createForm: { name: '', description: '' }
    });
  },

  // 隐藏创建圈子弹框
  hideCreateModal: function() {
    this.setData({ showCreateModal: false });
  },

  // 创建圈子表单输入
  onCreateNameInput: function(e) {
    this.setData({
      'createForm.name': e.detail.value
    });
  },

  onCreateDescInput: function(e) {
    this.setData({
      'createForm.description': e.detail.value
    });
  },

  // 创建圈子
  createCircle: function() {
    const { name, description } = this.data.createForm;
    
    if (!name.trim()) {
      wx.showToast({
        title: '请输入圈子名称',
        icon: 'none'
      });
      return;
    }

    if (name.trim().length > 50) {
      wx.showToast({
        title: '圈子名称不能超过50个字符',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    wx.request({
      url: app.globalData.apiBaseUrl + '/create_outfit_circle.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        name: name.trim(),
        description: description.trim()
      },
      success: (res) => {
        console.log('创建圈子响应:', res.data);
        
        if (res.data.status === 'success') {
          wx.showToast({
            title: '圈子创建成功',
            icon: 'success'
          });
          
          this.setData({ 
            showCreateModal: false,
            submitting: false
          });
          
          // 重新加载圈子信息
          this.loadCircleInfo();
        } else {
          wx.showToast({
            title: res.data.message || '创建失败',
            icon: 'none'
          });
          this.setData({ submitting: false });
        }
      },
      fail: (err) => {
        console.error('创建圈子失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ submitting: false });
      }
    });
  },

  // 显示加入圈子弹框
  showJoinCircle: function() {
    this.setData({ 
      showJoinModal: true,
      joinForm: { invitationCode: '' }
    });
  },

  // 隐藏加入圈子弹框
  hideJoinModal: function() {
    this.setData({ showJoinModal: false });
  },

  // 加入圈子表单输入
  onJoinCodeInput: function(e) {
    this.setData({
      'joinForm.invitationCode': e.detail.value.toUpperCase()
    });
  },

  // 加入圈子
  joinCircle: function() {
    const { invitationCode } = this.data.joinForm;
    
    if (!invitationCode.trim()) {
      wx.showToast({
        title: '请输入邀请码',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    wx.request({
      url: app.globalData.apiBaseUrl + '/join_outfit_circle.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        invitation_code: invitationCode.trim()
      },
      success: (res) => {
        console.log('加入圈子响应:', res.data);
        
        if (res.data.status === 'success') {
          wx.showToast({
            title: '成功加入圈子',
            icon: 'success'
          });
          
          this.setData({ 
            showJoinModal: false,
            submitting: false
          });
          
          // 重新加载圈子信息
          this.loadCircleInfo();
        } else {
          wx.showToast({
            title: res.data.message || '加入失败',
            icon: 'none'
          });
          this.setData({ submitting: false });
        }
      },
      fail: (err) => {
        console.error('加入圈子失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ submitting: false });
      }
    });
  },

  // 复制邀请码
  copyInvitationCode: function() {
    if (!this.data.circle || !this.data.circle.invitation_code) {
      return;
    }

    wx.setClipboardData({
      data: this.data.circle.invitation_code,
      success: () => {
        wx.showToast({
          title: '邀请码已复制',
          icon: 'success'
        });
      }
    });
  },

  // 显示退出圈子确认弹框
  showLeaveCircle: function() {
    wx.showModal({
      title: '退出圈子',
      content: '确定要退出当前圈子吗？退出后将无法查看圈子内的共享数据。',
      confirmText: '确定退出',
      confirmColor: '#ff3b30',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.leaveCircle();
        }
      }
    });
  },

  // 退出圈子
  leaveCircle: function() {
    wx.showLoading({
      title: '退出中...',
      mask: true
    });

    wx.request({
      url: app.globalData.apiBaseUrl + '/leave_circle.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('退出圈子响应:', res.data);

        if (res.data.status === 'success') {
          wx.showToast({
            title: '已退出圈子',
            icon: 'success'
          });

          // 重新加载圈子信息
          this.loadCircleInfo();
        } else {
          wx.showToast({
            title: res.data.message || '退出失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('退出圈子失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 显示踢出成员确认弹框
  showKickMember: function(e) {
    const member = e.currentTarget.dataset.member;
    if (!member) {
      return;
    }

    this.setData({ currentMember: member });

    wx.showModal({
      title: '移除成员',
      content: `确定要将"${member.nickname}"移出圈子吗？`,
      confirmText: '确定移除',
      confirmColor: '#ff3b30',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.kickMember(member);
        }
      }
    });
  },

  // 踢出成员
  kickMember: function(member) {
    wx.showLoading({
      title: '移除中...',
      mask: true
    });

    wx.request({
      url: app.globalData.apiBaseUrl + '/remove_circle_member.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        user_id: member.user_id
      },
      success: (res) => {
        wx.hideLoading();
        console.log('踢出成员响应:', res.data);

        if (res.data.status === 'success') {
          wx.showToast({
            title: '成员已移除',
            icon: 'success'
          });

          // 重新加载圈子信息
          this.loadCircleInfo();
        } else {
          wx.showToast({
            title: res.data.message || '移除失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('踢出成员失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 查看成员详情
  viewMemberDetail: function(e) {
    const member = e.currentTarget.dataset.member;
    if (!member) {
      return;
    }

    wx.request({
      url: app.globalData.apiBaseUrl + '/get_member_contributions.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        user_id: member.user_id
      },
      success: (res) => {
        console.log('获取成员详情响应:', res.data);

        if (res.data.status === 'success') {
          const memberDetail = res.data.data;

          // 显示成员详情弹框
          wx.showModal({
            title: `${memberDetail.user_info.nickname}的贡献`,
            content: `角色：${memberDetail.user_info.role === 'creator' ? '创建者' : '成员'}\n` +
                    `衣橱：${memberDetail.contributions.wardrobe_count}个\n` +
                    `衣物：${memberDetail.contributions.clothes_count}件\n` +
                    `穿搭：${memberDetail.contributions.outfit_count}套\n` +
                    `衣物分类：${memberDetail.contributions.clothing_category_count}个\n` +
                    `穿搭分类：${memberDetail.contributions.outfit_category_count}个\n` +
                    `标签：${memberDetail.contributions.tag_count}个\n` +
                    `总贡献：${memberDetail.contributions.total_contributions}项\n` +
                    `排名：第${memberDetail.contributions.rank}名`,
            showCancel: false,
            confirmText: '知道了'
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取详情失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取成员详情失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 显示被踢出通知
  showRemovedNotification: function(removedInfo) {
    const isKicked = !removedInfo.is_self_removed;
    const title = isKicked ? '您已被移出圈子' : '您已退出圈子';

    // 格式化时间显示
    const formatTime = (timeStr) => {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      const now = new Date();
      const diffMs = now - date;
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      } else if (diffDays === 1) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        return date.toLocaleDateString('zh-CN');
      }
    };

    const timeText = formatTime(removedInfo.removed_at);
    const content = isKicked
      ? `您已被${removedInfo.removed_by_nickname}从圈子"${removedInfo.circle_name}"中移除。${timeText ? '\n\n时间：' + timeText : ''}`
      : `您已主动退出圈子"${removedInfo.circle_name}"。${timeText ? '\n\n时间：' + timeText : ''}`;

    // 显示弹框，用户确认后调用清除API
    wx.showModal({
      title: title,
      content: content,
      showCancel: false,
      confirmText: '我知道了',
      confirmColor: isKicked ? '#ff3b30' : '#007aff',
      success: (res) => {
        if (res.confirm) {
          // 用户确认后，调用清除状态API
          this.clearRemovedStatus();

          // 同时保存到本地存储，基于被移除的时间戳
          const notificationKey = `notification_read_${app.globalData.userInfo?.id || 'unknown'}_${removedInfo.removed_at}`;
          wx.setStorageSync(notificationKey, true);
          console.log('保存通知已读状态:', notificationKey);
        }
      }
    });
  },

  // 清除被踢出状态（调用后端API标记已读）
  clearRemovedStatus: function(callback) {
    wx.request({
      url: app.globalData.apiBaseUrl + '/clear_removed_status.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('清除被踢出状态响应:', res.data);
        if (callback && typeof callback === 'function') {
          callback();
        }
      },
      fail: (err) => {
        console.error('清除被踢出状态失败:', err);
        // 即使API调用失败，也要执行回调，确保弹框能显示
        if (callback && typeof callback === 'function') {
          callback();
        }
      }
    });
  },

  // 清理过期的通知缓存（可选调用）
  clearExpiredNotificationCache: function() {
    try {
      const userId = app.globalData.userInfo?.id || 'unknown';
      const prefix = `notification_read_${userId}_`;

      // 获取所有存储的key
      const info = wx.getStorageInfoSync();
      const keysToRemove = info.keys.filter(key => key.startsWith(prefix));

      // 清理超过30天的缓存
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      keysToRemove.forEach(key => {
        // 从key中提取时间戳进行比较（这里简化处理）
        // 实际项目中可以根据需要实现更精确的过期逻辑
        wx.removeStorageSync(key);
      });

      console.log('清理了过期的通知缓存:', keysToRemove.length);
    } catch (e) {
      console.error('清理缓存失败:', e);
    }
  },

  // 跳转到数据共享页面并直接显示同步弹框
  goToSharedData: function() {
    console.log('点击数据共享按钮');
    wx.navigateTo({
      url: '/pages/outfit_circle/shared_data/shared_data?showSync=true',
      fail: function(err) {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 分享邀请
  shareInvitation: function() {
    if (!this.data.circle || !this.data.circle.invitation_code) {
      wx.showToast({
        title: '邀请码不存在',
        icon: 'none'
      });
      return;
    }

    // 先获取分享信息
    wx.request({
      url: app.globalData.apiBaseUrl + '/generate_share_invitation.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('生成分享邀请响应:', res.data);

        if (res.data.status === 'success') {
          // 记录分享行为
          this.recordShare('wechat');

          // 显示分享菜单
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline']
          });
        } else {
          wx.showToast({
            title: res.data.message || '生成分享信息失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('生成分享邀请失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 记录分享行为
  recordShare: function(shareType) {
    wx.request({
      url: app.globalData.apiBaseUrl + '/record_invitation_share.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        share_type: shareType
      },
      success: (res) => {
        console.log('记录分享响应:', res.data);
      },
      fail: (err) => {
        console.error('记录分享失败:', err);
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadCircleInfo();
    // 延迟停止下拉刷新动画
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 刷新页面
  onRefresh: function() {
    this.loadCircleInfo();
  },

  // 分享给好友
  onShareAppMessage: function() {
    if (!this.data.circle || this.data.userRole !== 'creator') {
      return {
        title: '次元衣帽间 - 共同管理衣橱穿搭',
        path: '/pages/index/index'
      };
    }

    // 记录分享行为
    this.recordShare('wechat');

    const userInfo = app.globalData.userInfo;
    const inviterName = userInfo ? userInfo.nickname : '朋友';

    return {
      title: `${inviterName}邀请您加入「${this.data.circle.name}」穿搭圈子`,
      path: `/pages/outfit_circle/invitation/invitation?code=${this.data.circle.invitation_code}&inviter=${userInfo ? userInfo.id : ''}`,
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline: function() {
    if (!this.data.circle || this.data.userRole !== 'creator') {
      return {
        title: '次元衣帽间 - 共同管理衣橱穿搭',
        query: ''
      };
    }

    // 记录分享行为
    this.recordShare('timeline');

    const userInfo = app.globalData.userInfo;
    const inviterName = userInfo ? userInfo.nickname : '朋友';

    return {
      title: `${inviterName}邀请您加入「${this.data.circle.name}」穿搭圈子`,
      query: `code=${this.data.circle.invitation_code}&inviter=${userInfo ? userInfo.id : ''}`,
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png'
    };
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 阻止事件冒泡，防止点击弹框内容时关闭弹框
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  },

  // 阻止创建弹框关闭
  stopCreateModalClose: function(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    return false;
  },

  // 阻止加入弹框关闭
  stopJoinModalClose: function(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    return false;
  }
});
