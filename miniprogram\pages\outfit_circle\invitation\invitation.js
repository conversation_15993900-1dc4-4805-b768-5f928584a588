// 圈子邀请页面
// 模块3：邀请分享模块

const app = getApp();

Page({
  data: {
    loading: true,
    invitationCode: '',
    inviterId: null,
    circle: null,
    inviter: null,
    userStatus: 'unknown', // unknown, can_join, already_member, in_other_circle, can_rejoin
    statusMessage: '',
    submitting: false,
    showLoginModal: false
  },

  onLoad: function(options) {
    console.log('邀请页面参数:', options);
    
    // 获取邀请码和邀请者ID
    const invitationCode = options.code;
    const inviterId = options.inviter ? parseInt(options.inviter) : null;
    
    if (!invitationCode) {
      wx.showToast({
        title: '邀请码无效',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }
    
    this.setData({
      invitationCode: invitationCode.toUpperCase(),
      inviterId: inviterId
    });
    
    this.loadInvitationInfo();
  },

  // 加载邀请信息
  loadInvitationInfo: function() {
    this.setData({ loading: true });
    
    const params = new URLSearchParams({
      code: this.data.invitationCode
    });
    
    if (this.data.inviterId) {
      params.append('inviter', this.data.inviterId);
    }
    
    wx.request({
      url: app.globalData.apiBaseUrl + '/get_invitation_info.php?' + params.toString(),
      method: 'GET',
      header: {
        'Authorization': app.globalData.token ? 'Bearer ' + app.globalData.token : '',
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('获取邀请信息响应:', res.data);
        
        if (res.data.status === 'success') {
          this.setData({
            circle: res.data.data.circle,
            inviter: res.data.data.inviter,
            userStatus: res.data.data.user_status,
            statusMessage: res.data.data.status_message,
            loading: false
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取邀请信息失败',
            icon: 'none'
          });
          this.setData({ loading: false });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      },
      fail: (err) => {
        console.error('获取邀请信息失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 同意加入圈子
  agreeToJoin: function() {
    // 检查用户是否已登录
    if (!app.globalData.token || !app.globalData.userInfo) {
      this.setData({ showLoginModal: true });
      return;
    }
    
    // 检查用户状态
    if (this.data.userStatus === 'already_member') {
      wx.showToast({
        title: '您已经是该圈子的成员',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.userStatus === 'in_other_circle') {
      wx.showToast({
        title: this.data.statusMessage,
        icon: 'none'
      });
      return;
    }
    
    this.setData({ submitting: true });
    
    wx.request({
      url: app.globalData.apiBaseUrl + '/join_outfit_circle.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        invitation_code: this.data.invitationCode
      },
      success: (res) => {
        console.log('加入圈子响应:', res.data);
        
        if (res.data.status === 'success') {
          wx.showToast({
            title: '成功加入圈子',
            icon: 'success'
          });
          
          // 延迟跳转到圈子主页
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/outfit_circle/index/index'
            });
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.message || '加入失败',
            icon: 'none'
          });
          this.setData({ submitting: false });
        }
      },
      fail: (err) => {
        console.error('加入圈子失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ submitting: false });
      }
    });
  },

  // 拒绝加入
  refuseToJoin: function() {
    wx.showModal({
      title: '确认拒绝',
      content: '您确定要拒绝加入这个圈子吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 显示登录弹框
  showLogin: function() {
    this.setData({ showLoginModal: true });
  },

  // 隐藏登录弹框
  hideLoginModal: function() {
    this.setData({ showLoginModal: false });
  },

  // 跳转到登录页面
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login?redirect=outfit_circle_invitation&code=' + this.data.invitationCode + '&inviter=' + (this.data.inviterId || '')
    });
  },

  // 阻止弹框关闭
  stopModalClose: function() {
    // 阻止事件冒泡，防止点击弹框内容时关闭弹框
  },

  // 复制邀请码
  copyInvitationCode: function() {
    wx.setClipboardData({
      data: this.data.invitationCode,
      success: () => {
        wx.showToast({
          title: '邀请码已复制',
          icon: 'success'
        });
      }
    });
  },

  // 分享给好友
  onShareAppMessage: function() {
    if (!this.data.circle) {
      return {
        title: '次元衣帽间 - 共同管理衣橱穿搭',
        path: '/pages/index/index'
      };
    }
    
    const inviterName = this.data.inviter ? this.data.inviter.nickname : '朋友';
    
    return {
      title: `${inviterName}邀请您加入「${this.data.circle.name}」穿搭圈子`,
      path: `/pages/outfit_circle/invitation/invitation?code=${this.data.invitationCode}&inviter=${this.data.inviterId || ''}`,
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline: function() {
    if (!this.data.circle) {
      return {
        title: '次元衣帽间 - 共同管理衣橱穿搭',
        query: ''
      };
    }
    
    const inviterName = this.data.inviter ? this.data.inviter.nickname : '朋友';
    
    return {
      title: `${inviterName}邀请您加入「${this.data.circle.name}」穿搭圈子`,
      query: `code=${this.data.invitationCode}&inviter=${this.data.inviterId || ''}`,
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png'
    };
  }
});
